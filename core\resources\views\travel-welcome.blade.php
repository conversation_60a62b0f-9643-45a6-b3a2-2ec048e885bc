<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>عروض السفر للحج والعمرة - Travel Offers Hajj & Umrah Company</title>
    <link rel="icon" href="{{ asset('assets/static/img/logo/logo/favicon.png') }}" type="image/png">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600;700&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2E7D32 0%, #1B5E20 50%, #0D47A1 100%);
            min-height: 100vh;
            direction: rtl;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .hero {
            text-align: center;
            padding: 80px 20px;
            color: white;
        }
        
        .hero h1 {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .hero p {
            font-size: 1.3rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 60px;
        }
        
        .feature-card {
            background: white;
            padding: 40px 30px;
            border-radius: 20px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
        }
        
        .feature-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }
        
        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #2E7D32;
        }
        
        .feature-card p {
            color: #666;
            line-height: 1.6;
        }
        
        .cta-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 40px;
        }
        
        .btn {
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #2E7D32, #1B5E20);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #FF9800, #F57C00);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        
        .stats {
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin: 60px 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            text-align: center;
            color: white;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: #FFD700;
        }
        
        .stat-label {
            font-size: 1.1rem;
            margin-top: 10px;
        }
        
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="hero">
            <h1>🕋 عروض السفر للحج والعمرة</h1>
            <p>خدمتكم شرف لنا - رحلات مباركة إلى بيت الله الحرام والمسجد النبوي الشريف</p>
            <div style="font-size: 1.1rem; margin: 20px 0; opacity: 0.9;">
                ✨ خدمات متكاملة • أسعار منافسة • راحة وأمان ✨
            </div>

            <div class="cta-buttons">
                <a href="/admin" class="btn btn-primary">🏢 لوحة التحكم الإدارية</a>
                <a href="/register" class="btn btn-secondary">📝 سجل معنا الآن</a>
            </div>
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">15+</div>
                <div class="stat-label">سنة خبرة</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">10000+</div>
                <div class="stat-label">حاج ومعتمر</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">خدمة عملاء</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">رضا العملاء</div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">🕋</div>
                <h3>رحلات الحج المبرور</h3>
                <p>برامج حج متنوعة تشمل الإقامة في أفضل الفنادق القريبة من الحرم مع مرشدين دينيين متخصصين</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🌙</div>
                <h3>عمرة مباركة</h3>
                <p>برامج عمرة على مدار السنة مع خدمات VIP وإقامة فاخرة وزيارة المعالم الإسلامية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">✈️</div>
                <h3>طيران مباشر</h3>
                <p>رحلات طيران مباشرة إلى جدة والمدينة المنورة مع أفضل شركات الطيران العالمية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🏨</div>
                <h3>إقامة مميزة</h3>
                <p>فنادق 5 نجوم قريبة من الحرم المكي والمسجد النبوي مع خدمات استثنائية</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🚌</div>
                <h3>نقل مكيف</h3>
                <p>حافلات مكيفة حديثة للتنقل بين المشاعر المقدسة مع سائقين ذوي خبرة</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">👨‍🏫</div>
                <h3>إرشاد ديني</h3>
                <p>مرشدون دينيون متخصصون لتعليم مناسك الحج والعمرة وتقديم الدعم الروحي</p>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 60px; color: white;">
            <h2>🤲 تقبل الله حجكم وعمرتكم</h2>
            <p style="margin-top: 20px; font-size: 1.1rem;">
                نحن هنا لخدمتكم في رحلتكم المباركة إلى بيت الله الحرام
            </p>
            <p style="margin-top: 10px; opacity: 0.8;">
                "وَأَذِّن فِي النَّاسِ بِالْحَجِّ يَأْتُوكَ رِجَالًا وَعَلَىٰ كُلِّ ضَامِرٍ يَأْتِينَ مِن كُلِّ فَجٍّ عَمِيقٍ"
            </p>
            <div style="margin-top: 30px;">
                <a href="tel:+965-XXXX-XXXX" class="btn btn-primary">📞 اتصل بنا الآن</a>
                <a href="mailto:<EMAIL>" class="btn btn-secondary">✉️ راسلنا</a>
            </div>
        </div>
    </div>
</body>
</html>
