@extends('backend.layout.master')
@section('title', __('لوحة التحكم - عروض السفر للحج والعمرة'))
@section('style')
<style>
    .hajj-dashboard-header {
        background: linear-gradient(135deg, #2E7D32, #1B5E20);
        color: white;
        padding: 2rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .hajj-promo {
        background: linear-gradient(135deg, #2E7D32, #1B5E20);
        color: white;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .hajj-promo:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(46, 125, 50, 0.3);
    }

    .umrah-promo {
        background: linear-gradient(135deg, #FF9800, #F57C00);
        color: white;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    .umrah-promo:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(255, 152, 0, 0.3);
    }

    .islamic-quote {
        background: rgba(46, 125, 50, 0.1);
        border-right: 4px solid #2E7D32;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 5px;
        font-style: italic;
        color: #2E7D32;
    }
</style>
@endsection

@section('content')
    <div class="dashboard__body">
        <!-- Islamic Welcome Header -->
        <div class="hajj-dashboard-header">
            <h2>🕋 مرحباً بكم في لوحة تحكم عروض السفر للحج والعمرة</h2>
            <p>خدمتكم شرف لنا في إدارة رحلات ضيوف الرحمن</p>
            <div class="islamic-quote">
                "وَأَذِّن فِي النَّاسِ بِالْحَجِّ يَأْتُوكَ رِجَالًا وَعَلَىٰ كُلِّ ضَامِرٍ يَأْتِينَ مِن كُلِّ فَجٍّ عَمِيقٍ"
            </div>
        </div>

        <div class="row g-4">
            <div class="col-xxl-8 col-lg-12">
                <div class="dashboard__promo bg-white">
                    <div class="dashboard__promo__row">
                        <a href="{{ route('admin.order.all') }}" class="dashboard__promo__col promo_child">
                            <div class="single_promo hajj-promo">
                                <div class="single_promo__contents">
                                    <span class="single_promo__subtitle">🕋 {{ __('إجمالي الإيرادات') }}</span>
                                    <h4 class="single_promo__title mt-2"> {{ float_amount_with_currency_symbol($total_revenue) ?? '' }} </h4>
                                </div>
                            </div>
                        </a>
                        <a href="{{ route('admin.jobs') }}" class="dashboard__promo__col promo_child">
                            <div class="single_promo umrah-promo">
                                <div class="single_promo__contents">
                                    <span class="single_promo__subtitle">🌙 {{ __('برامج الحج والعمرة') }}</span>
                                    <h4 class="single_promo__title mt-2">{{ $total_job ?? '' }} </h4>
                                </div>
                            </div>
                        </a>
                        <a href="{{ route('admin.freelancer.all') }}" class="dashboard__promo__col promo_child">
                            <div class="single_promo hajj-promo">
                                <div class="single_promo__contents">
                                    <span class="single_promo__subtitle">🏢 {{ __('شركات الحج والعمرة') }}</span>
                                    <h4 class="single_promo__title mt-2"> {{ $total_freelancer ?? '' }} </h4>
                                </div>
                            </div>
                        </a>
                        <a href="{{ route('admin.client.all') }}" class="dashboard__promo__col promo_child">
                            <div class="single_promo umrah-promo">
                                <div class="single_promo__contents">
                                    <span class="single_promo__subtitle">👥 {{ __('الحجاج والمعتمرين') }}</span>
                                    <h4 class="single_promo__title mt-2"> {{ $total_client ?? '' }}</h4>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="dashboard__charts padding-20 radius-10 bg-white mt-4">
                    <div class="dashboard__charts__header flex-between align-items-center">
                        <h4 class="dashboard__charts__title">{{ __('Revenue') }}</h4>
                        <div class="dashboard__select">
                          <strong>{{ __('Monthly Revenue') }}</strong>
                        </div>
                    </div>
                    <div class="dashboard__charts__inner profile-border-top">
                        <canvas id="bar-chart-grouped"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-lg-8">
                <div class="dashboard__maps bg-white padding-20 radius-10">
                    <div class="dashboard__maps__flex flex-between align-items-center">
                        <h4 class="dashboard__maps__title">{{ __('Quick Access') }}</h4>
                    </div>
                    <div class="dashboard__maps__footer mt-4">
                        <h6 class="dashboard__maps__footer__title">{{ __('System Settings By Super Admin') }}</h6>
                        <ul class="dashboard__maps__footer__list mt-4">
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Commission Type') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ ucfirst(get_static_option('admin_commission_type') ?? '') }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Commission Charge') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ get_static_option('admin_commission_charge') ?? '' }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Transaction Fee Type') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ ucfirst(get_static_option('transaction_fee_type') ?? '') }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Transaction Fee Charge') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ get_static_option('transaction_fee_charge') ?? '' }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Connect Reduce Per Proposal') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ get_static_option('limit_settings') ?? 1 }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Job Auto Approval') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ ucfirst(get_static_option('job_auto_approval')) }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Withdraw Fee') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ float_amount_with_currency_symbol(get_static_option('withdraw_fee')) ?? 0 }}</span>
                            </li>
                            <li class="dashboard__maps__footer__list_item">
                                <span class="dashboard__maps__footer__list__country">{{ __('Maximum Deposit Amount') }}</span>
                                <span class="dashboard__maps__footer__list__count">{{ float_amount_with_currency_symbol(get_static_option('deposit_amount_limitation_for_user')) ?? 0 }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col">
                <div class="activities">
                    <div class="activities-single radius-10 padding-20">
                        <div class="activities-single-header profile-border-bottom flex-between align-items-center">
                            <h4 class="activities-single-header-title">{{ __('Recent Orders') }}</h4>
                        </div>
                        <div class="dashboard-tab-content-item active" id="Transactions">
                            <div class="activities-single-table mt-4">
                                <table class="table table-responsive">
                                    <thead>
                                        <tr>
                                            <th col="scope">{{ __('User ID') }}</th>
                                            <th col="scope">{{ __('Type') }}</th>
                                            <th col="scope">{{ __('Price') }}</th>
                                            <th col="scope">{{ __('Payment Gateway') }}</th>
                                            <th col="scope">{{ __('Payment Status') }}</th>
                                            <th col="scope">{{ __('Status') }}</th>
                                            <th col="scope">{{ __('Order Date') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($orders as $order)
                                    <tr>
                                        <td>{{ $order->user_id ?? '' }}</td>
                                        <td>{{ ucfirst($order->is_project_job) }}</td>
                                        <td>{{ float_amount_with_currency_symbol($order->price) }}</td>
                                        <td>
                                            @if($order->payment_gateway == 'manual_payment')
                                                {{ ucfirst(str_replace('_',' ',$order->payment_gateway)) }}
                                            @else
                                                {{ $order->payment_gateway == 'authorize_dot_net' ? __('Authorize.Net') : ucfirst($order->payment_gateway) }}
                                            @endif
                                        </td>
                                        <td>
                                            @if($order->payment_status == '' || $order->payment_status == 'cancel')
                                                <span class="btn btn-danger btn-sm">{{ __('Cancel') }}</span>
                                            @elseif($order->payment_status == 'pending')
                                                <span class="btn btn-warning btn-sm">{{ ucfirst($order->payment_status) }}</span>
                                            @else
                                                <span class="btn btn-success btn-sm">{{ ucfirst($order->payment_status) }}</span>
                                            @endif
                                        </td>
                                        <td> <x-status.table.order-status :status="$order->status"/> </td>
                                        <td>{{ $order->created_at->format('Y-m-d') ?? '' }}</td>
                                    </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
@endsection

@section('script')
    <script>
        (function($){
            "use strict";
            $(document).ready(function(){
                //monthly income
                new Chart(document.getElementById("bar-chart-grouped"), {
                    type: 'bar',
                    data: {
                        labels: [@foreach($month_list as $list) "{{ $list }}", @endforeach],
                        datasets: [{
                            label: "{{ __('Revenue') }}",
                            backgroundColor: "#6176F6",
                            data: [@foreach($monthly_income as $income) "{{ $income  }}", @endforeach],
                            barThickness: 15,
                            hoverBackgroundColor: '#fff',
                            hoverBorderColor: '#6176F6',
                            borderColor: '#fff',
                            borderWidth: 2,
                        }],
                    },
                    options: {
                        scales: {
                            x: {
                                grid: {
                                    display: false,
                                }
                            },
                            y: {
                                grid: {
                                    display: false,
                                }
                            },
                        }
                    }
                });
            });
        }(jQuery));

    </script>
@endsection
