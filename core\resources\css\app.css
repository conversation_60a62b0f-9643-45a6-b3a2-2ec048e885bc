/* عروض السفر للحج والعمرة - Al-Haramain Hajj & Umrah Company */

/* Arabic RTL Support */
.rtl {
    direction: rtl;
    text-align: right;
}

/* Hajj & Umrah Theme Colors */
:root {
    --hajj-primary: #2E7D32;        /* أخضر إسلامي */
    --hajj-secondary: #1B5E20;      /* أخضر داكن */
    --hajj-accent: #FF9800;         /* ذهبي */
    --hajj-gold: #FFD700;           /* ذهبي فاتح */
    --hajj-success: #4CAF50;        /* أخضر النجاح */
    --hajj-danger: #F44336;         /* أحمر */
    --hajj-warning: #FFC107;        /* أصفر تحذيري */
    --hajj-info: #2196F3;           /* أزرق معلوماتي */
    --hajj-kaaba: #1A1A1A;          /* أسود الكعبة */
    --hajj-white: #FFFFFF;          /* أبيض الإحرام */
    --hajj-cream: #F5F5DC;          /* كريمي */
}

/* Hajj & <PERSON><PERSON> Card Styles */
.hajj-card {
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(46, 125, 50, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--hajj-primary);
    background: var(--hajj-white);
}

.hajj-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(46, 125, 50, 0.2);
}

/* Islamic Design Elements */
.islamic-pattern {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232E7D32' fill-opacity='0.05'%3E%3Cpath d='M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

.kaaba-icon {
    color: var(--hajj-kaaba);
    font-size: 2rem;
}

.crescent-icon {
    color: var(--hajj-accent);
    font-size: 1.5rem;
}

/* Travel Button Styles */
.btn-travel {
    background: linear-gradient(135deg, var(--travel-primary), var(--travel-secondary));
    border: none;
    border-radius: 8px;
    color: white;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-travel:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(97, 118, 246, 0.3);
}

/* Travel Icons */
.travel-icon {
    width: 24px;
    height: 24px;
    fill: var(--travel-primary);
}

/* Arabic Font Support */
.arabic-text {
    font-family: 'Cairo', 'Tajawal', 'Amiri', sans-serif;
    font-weight: 400;
}

/* Travel Dashboard Styles */
.travel-dashboard {
    background: linear-gradient(135deg, #f8f9ff, #e8f0ff);
    min-height: 100vh;
}

/* Travel Navigation */
.travel-nav {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-bottom: 3px solid var(--travel-primary);
}

/* Travel Category Styles */
.travel-category {
    background: linear-gradient(135deg, var(--travel-primary), var(--travel-secondary));
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.travel-category:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 30px rgba(30, 136, 229, 0.3);
}

.travel-category-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--travel-accent);
}

/* Travel Hero Section */
.travel-hero {
    background: linear-gradient(135deg, var(--travel-sky), var(--travel-primary));
    color: white;
    padding: 80px 0;
    text-align: center;
}

.travel-hero h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.travel-hero p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Travel Search Box */
.travel-search {
    background: white;
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    margin-top: -50px;
    position: relative;
    z-index: 10;
}

/* Travel Stats */
.travel-stats {
    background: var(--travel-nature);
    color: white;
    padding: 60px 0;
}

.travel-stat-item {
    text-align: center;
    padding: 20px;
}

.travel-stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: var(--travel-accent);
}

.travel-stat-label {
    font-size: 1.1rem;
    margin-top: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .travel-card {
        margin-bottom: 16px;
    }

    .btn-travel {
        width: 100%;
        margin-bottom: 8px;
    }

    .travel-hero h1 {
        font-size: 2.5rem;
    }

    .travel-search {
        margin: 20px;
        padding: 20px;
    }
}