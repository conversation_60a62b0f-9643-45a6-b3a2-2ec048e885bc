<?xml version="1.0" encoding="UTF-8"?>
<svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="greenGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2E7D32;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1B5E20;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="goldGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF9800;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <circle cx="16" cy="16" r="15" fill="url(#greenGrad)"/>
  
  <!-- Kaaba -->
  <rect x="10" y="10" width="12" height="12" rx="1" fill="#1A1A1A"/>
  <rect x="11" y="11" width="10" height="10" rx="0.5" fill="none" stroke="url(#goldGrad)" stroke-width="0.5"/>
  
  <!-- Crescent -->
  <path d="M 20 6 Q 24 8 24 12 Q 24 16 20 18 Q 22 14 22 12 Q 22 10 20 6 Z" fill="url(#goldGrad)"/>
  
  <!-- Star -->
  <polygon points="26,8 27,10 29,10 27.5,11.5 28,13.5 26,12.5 24,13.5 24.5,11.5 23,10 25,10" fill="url(#goldGrad)"/>
</svg>
