1753904139O:23:"App\Models\StaticOption":30:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"static_options";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:318;s:11:"option_name";s:13:"site_tag_line";s:12:"option_value";s:30:"Freelance Services Marketplace";s:10:"created_at";s:19:"2023-05-09 04:09:04";s:10:"updated_at";s:19:"2023-11-13 09:45:07";}s:11:" * original";a:5:{s:2:"id";i:318;s:11:"option_name";s:13:"site_tag_line";s:12:"option_value";s:30:"Freelance Services Marketplace";s:10:"created_at";s:19:"2023-05-09 04:09:04";s:10:"updated_at";s:19:"2023-11-13 09:45:07";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:2:{i:0;s:11:"option_name";i:1;s:12:"option_value";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}