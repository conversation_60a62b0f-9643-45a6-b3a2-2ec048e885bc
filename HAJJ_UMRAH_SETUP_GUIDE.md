# 🕋 دليل إعداد عروض السفر للحج والعمرة

## 📋 نظرة عامة

تم تخصيص هذا المشروع بالكامل ليصبح **عروض السفر للحج والعمرة** - منصة متكاملة لإدارة رحلات الحج والعمرة مع تطبيقات الهاتف المحمول ولوحة تحكم إدارية.

## 🎯 التخصيصات المكتملة

### ✅ 1. الواجهة الأمامية (Frontend)
- **الصفحة الرئيسية**: تم تخصيصها بالكامل لشركة الحج والعمرة
- **الألوان**: ألوان إسلامية (أخضر، ذهبي، أبيض)
- **الخطوط**: خطوط عربية مناسبة (Cairo, Amiri, Noto Sans Arabic)
- **الشعار**: شعار مخصص مع رموز إسلامية (الكعبة، الهلال، النجمة)

### ✅ 2. الترجمة والنصوص
- **ملف الترجمة العربي**: محدث بالكامل مع مصطلحات الحج والعمرة
- **النصوص**: تم استبدال جميع النصوص لتناسب الطابع الديني
- **المصطلحات**: 
  - Freelancer → شركة الحج والعمرة
  - Client → الحاج والمعتمر
  - Job → برنامج الحج والعمرة
  - Project → رحلة مقدسة

### ✅ 3. قاعدة البيانات
- **الفئات**: محدثة لتشمل خدمات الحج والعمرة
- **الفئات الفرعية**: حج فردي/جماعي، عمرة فردية/جماعية
- **المهارات**: خدمات دينية متخصصة
- **الإعدادات**: ألوان وإعدادات مخصصة

### ✅ 4. الصفحات المخصصة
- **صفحة برامج الحج**: عرض شامل لبرامج الحج المختلفة
- **صفحة برامج العمرة**: برامج عمرة متنوعة
- **صفحة عن الشركة**: تاريخ وقيم الشركة
- **صفحة الترحيب**: صفحة ترحيب مخصصة

### ✅ 5. لوحة التحكم
- **التصميم**: ألوان وتصميم إسلامي
- **الإحصائيات**: مخصصة لإدارة الحج والعمرة
- **النصوص**: محدثة لتناسب الطابع الديني

## 🚀 خطوات التشغيل

### 1. إعداد قاعدة البيانات
```bash
# إنشاء قاعدة بيانات جديدة
mysql -u root -p
CREATE DATABASE alharamain_hajj_umrah;

# استيراد قاعدة البيانات الأساسية
mysql -u username -p alharamain_hajj_umrah < install/database.sql

# تطبيق التحديثات المخصصة للحج والعمرة
mysql -u username -p alharamain_hajj_umrah < install/hajj-umrah-updates.sql
```

### 2. إعداد Laravel Backend
```bash
cd core
cp .env.example .env

# تحديث إعدادات قاعدة البيانات في .env
DB_DATABASE=alharamain_hajj_umrah
DB_USERNAME=your_username
DB_PASSWORD=your_password

# تثبيت المتطلبات وإعداد المفاتيح
composer install
php artisan key:generate
php artisan migrate
php artisan serve
```

### 3. إعداد تطبيق العملاء (clientapp)
```bash
cd clientapp
flutter pub get
flutter run
```

### 4. إعداد تطبيق شركات الحج والعمرة (offerapp)
```bash
cd offerapp
flutter pub get
flutter run
```

## 🎨 الألوان المستخدمة

```css
:root {
    --hajj-primary: #2E7D32;        /* أخضر إسلامي */
    --hajj-secondary: #1B5E20;      /* أخضر داكن */
    --hajj-accent: #FF9800;         /* ذهبي */
    --hajj-gold: #FFD700;           /* ذهبي فاتح */
    --hajj-kaaba: #1A1A1A;          /* أسود الكعبة */
    --hajj-white: #FFFFFF;          /* أبيض الإحرام */
}
```

## 📱 الصفحات المتاحة

### الواجهة الأمامية:
- `/` - الصفحة الرئيسية
- `/hajj-packages` - برامج الحج
- `/umrah-packages` - برامج العمرة
- `/about-company` - عن الشركة
- `/travel-welcome` - صفحة الترحيب

### لوحة التحكم:
- `/admin` - لوحة التحكم الرئيسية
- `/admin/jobs` - إدارة برامج الحج والعمرة
- `/admin/freelancer` - إدارة شركات الحج والعمرة
- `/admin/client` - إدارة الحجاج والمعتمرين

## 🔧 التخصيصات الإضافية

### إضافة برنامج حج جديد:
1. اذهب إلى لوحة التحكم
2. اختر "برامج الحج والعمرة"
3. انقر "إضافة برنامج جديد"
4. املأ التفاصيل (السعر، المدة، الخدمات)

### تحديث معلومات الشركة:
1. لوحة التحكم → الإعدادات العامة
2. تحديث اسم الشركة والشعار
3. تحديث معلومات الاتصال

### إضافة خدمة جديدة:
1. لوحة التحكم → المهارات/الخدمات
2. إضافة خدمة جديدة مع الوصف
3. ربطها بالفئة المناسبة

## 📞 معلومات الاتصال المحدثة

- **الشركة**: عروض السفر للحج والعمرة
- **الهاتف**: +965-XXXX-XXXX
- **البريد الإلكتروني**: <EMAIL>
- **العنوان**: الكويت - منطقة السالمية
- **الموقع**: www.alharamain.com

## 🌐 اللغات المدعومة

- **العربية** (الأساسية)
- **الإنجليزية**
- **التركية**
- **البرتغالية**

## 🔐 الأمان والخصوصية

- تشفير البيانات الحساسة
- مصادقة ثنائية العامل
- حماية معلومات الدفع
- سياسة خصوصية محدثة
- تأمين معلومات الحجاج

## 📊 الإحصائيات المتاحة

- عدد الحجاج والمعتمرين المسجلين
- إجمالي الإيرادات
- عدد البرامج المتاحة
- معدل رضا العملاء
- إحصائيات شهرية وسنوية

## 🎯 المميزات الخاصة

### للحجاج والمعتمرين:
- البحث عن برامج الحج والعمرة
- مقارنة الأسعار والخدمات
- حجز الرحلات والفنادق
- تتبع حالة الحجز
- تقييم الخدمات

### لشركات الحج والعمرة:
- إنشاء برامج حج وعمرة
- إدارة الحجوزات
- تتبع الأرباح
- التواصل مع العملاء
- إدارة الخدمات

## 🤝 الدعم والمساعدة

للحصول على الدعم أو الإبلاغ عن مشاكل:
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965-XXXX-XXXX
- **واتساب**: +965-XXXX-XXXX

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم التطوير والتخصيص بواسطة**: Augment Agent  
**التاريخ**: 2025-01-30  
**الإصدار**: 2.0.0 - إصدار عروض السفر للحج والعمرة

**تقبل الله حجكم وعمرتكم** 🤲
