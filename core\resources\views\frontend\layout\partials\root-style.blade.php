<style>
    :root {
        /* <PERSON><PERSON><PERSON> & <PERSON><PERSON> Theme Colors */
        --main-color-one: {{ get_static_option('main_color_one') ?? '#2E7D32' }};
        --main-color-two: {{ get_static_option('main_color_two') ?? '#1B5E20' }};
        --main-color-one-rgb: {{ '46, 125, 50' }};
        --secondary-color: {{ get_static_option('secondary_color') ?? '#FF9800' }};
        --secondary-color-rgb: {{ '255, 152, 0' }};
        --bg-gradient: {{ 'linear-gradient(135deg, #2E7D32 0%, #1B5E20 50%, #0D47A1 100%)' }};
        --section-bg-base: {{ '#2E7D32' }};
        --section-bg-1: {{ '#F1F8E9' }};
        --section-bg-2: {{ '#E8F5E8' }};
        --footer-bg-1: {{ '#1B5E20' }};
        --footer-bg-2: {{ '#2E7D32' }};
        --copyright-bg-1: {{ '#0D47A1' }};
        --border-color: {{ '#C8E6C9' }};
        --border-color-2: {{ '#A5D6A7' }};
        --heading-color: {{ get_static_option('heading_color','#1B5E20') }};
        --paragraph-color: {{ get_static_option('paragraph_color','#2E7D32') }};
        --body-color: {{ get_static_option('body_color','#666') }};
        --white: {{ '#fff' }};
        --active-color: {{ '#4CAF50' }};
        --active-color-rgb: {{ '76, 175, 80' }};
        --success-color: {{ '#4CAF50' }};
        --success-color-rgb: {{ '76, 175, 80' }};
        --danger-color: {{ '#F44336' }};
        --danger-color-rgb: {{ '244, 67, 54' }};
        --hajj-gold: {{ '#FFD700' }};
        --hajj-gold-rgb: {{ '255, 215, 0' }};
        --promo-one: {{ '#E8F5E8' }};
        --promo-two: {{ '#FFF3E0' }};
        --promo-three: {{ '#E3F2FD' }};
        --promo-four: {{ '#F1F8E9' }};
        --promo-five: {{ '#FFEBEE' }};
        --promo-six: {{ '#E0F2F1' }};
        --promo-seven: {{ '#E8EAF6' }};
        --promo-eight: {{ '#FFF8E1' }};
        --heading-font: {{ get_static_option('heading_font_family') ?? 'Cairo' }}, 'Amiri', serif;
        --body-font: {{ get_static_option('body_font_family') ?? 'Cairo' }}, 'Noto Sans Arabic', sans-serif;
        --Otomanopee-font: {{get_static_option('section_font_family')}},sans-serif;
        {{----Otomanopee-font: {{ "Aclonica", "sans-serif" }};--}}
        {{----Otomanopee-font: {{ "Otomanopee", "sans-serif" }};--}}
    }
</style>